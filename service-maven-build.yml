variables:
  GLOBAL_CI_TOKEN: "jE6my5YJT8df7a4acmpe"

stages:
  - build
  - notify

deploy:
  stage: build
  script:
    - |
      set +e   # 先关闭 "出错即退出"
      if [ -z "${MODULE_NAME:-}" ]; then
        echo "MODULE_NAME 为空，执行全量构建"
        mvn clean deploy 2>&1 | tee build.log
      else
        echo "MODULE_NAME=$MODULE_NAME，执行模块构建"
        mvn clean deploy -pl "$MODULE_NAME" -am 2>&1 | tee build.log
      fi
      STATUS=${PIPESTATUS[0]}
      set -e   # 恢复
      if [ $STATUS -ne 0 ]; then
        if grep -q "Repository does not allow updating assets: releases" build.log; then
          echo "✅ 检测到仓库不允许更新发布版本，代码已成功构建，只是推送失败，忽略错误"
          exit 0
        else
          echo "❌ 构建或上传失败，触发钉钉提醒"
          exit 1
        fi
      fi
  tags:
    - backend
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "trigger" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: on_success

notify-on-fail:
  stage: notify
  script:
    - |
      set -e
      echo "拉取 user-mapping.json..."
      curl --fail --show-error --header "PRIVATE-TOKEN: ${GLOBAL_CI_TOKEN}" \
           -o user-mapping.json \
           "https://git-internal.polyv.net/api/v4/projects/1510/repository/files/user-mapping.json/raw?ref=master"

      if [ ! -f user-mapping.json ]; then
        echo "❌ 拉取失败，user-mapping.json 不存在"
        exit 1
      fi

      PHONE=$(grep "\"$GITLAB_USER_LOGIN\"" user-mapping.json | sed -E 's/^[[:space:]]*".*": ?"([0-9]+)".*/\1/')

      if [ -z "$PHONE" ]; then
        echo "没有找到手机号映射，使用默认"
        PHONE=""
      fi

      CONTENT="[polyv-ci] ❌ 构建失败通知\n项目：$CI_PROJECT_NAME\n分支：$CI_COMMIT_REF_NAME\n提交人：$GITLAB_USER_NAME\n提交信息：$CI_COMMIT_MESSAGE\n链接：$CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID"

      curl -s -X POST \
           -H 'Content-Type: application/json' \
           -d "{
                 \"msgtype\": \"text\",
                 \"text\": { \"content\": \"${CONTENT}\" },
                 \"at\": { \"atMobiles\": [\"${PHONE}\"], \"isAtAll\": false }
               }" \
        "https://oapi.dingtalk.com/robot/send?access_token=8a083421651abec5cf77878a3a29e9da286fddfc4b7bf836cb8ef2d7187bfeaf"
  tags:
    - backend
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "trigger" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: on_failure

notify-success:
  stage: notify
  needs:
    - deploy
  script:
    - |
      set -euo pipefail

      # 跳过 master 分支
      if [[ "$CI_COMMIT_REF_NAME" == "master" ]]; then
        echo "master 分支，跳过所有 downstream 触发。"
        exit 0
      fi
      
      # 提交信息
      echo "Commit message: $CI_COMMIT_MESSAGE"

      # 目标项目列表：格式 project_id
      declare -A targets
      targets["live-webapp-aggregate"]="823"
      targets["live-admin-aggregate"]="822"
      targets["live-watch-aggregate"]="824"
      targets["live-api-cloud"]="825"
      targets["live-background-aggregate"]="655"
      targets["watch-api-aggregate"]="1003"
      targets["console-center-aggregate"]="658"
      targets["live-api-aggregate"]="772"
      targets["group-account-aggregate"]="874"
      targets["vod-console-aggregate"]="1419"
      targets["vod-api-aggregate"]="960"
      targets["vod-api-cloud"]="1412"

      LATEST_COMMIT_MSG=$(git log --no-merges -1 --pretty=%B)
      echo "Latest non-merge commit message: $LATEST_COMMIT_MSG"
      
      # 使用关联数组去重
      declare -A trigger_projects

      # 1. commit message 命中的项目
      for project in "${!targets[@]}"; do
        if [[ "$CI_COMMIT_MESSAGE" == *"[$project]"* || "$LATEST_COMMIT_MSG" == *"[$project]"* ]]; then
          trigger_projects["$project"]=1
        fi
      done

      # 2. SUCCESS_TO_DEPLOY_MODULE 指定的项目
      if [[ -n "${SUCCESS_TO_DEPLOY_MODULE:-}" ]]; then
        IFS=',' read -ra extra_projects <<< "$SUCCESS_TO_DEPLOY_MODULE"
        for project in "${extra_projects[@]}"; do
          project=$(echo "$project" | xargs) # 去掉前后空格
          if [[ -n "${targets[$project]:-}" ]]; then
            trigger_projects["$project"]=1
          else
            echo "⚠️ SUCCESS_TO_DEPLOY_MODULE 指定了未知项目: $project"
          fi
        done
      fi
      
      # 触发所有收集到的项目
      for project in "${!trigger_projects[@]}"; do
        IFS='|' read -r project_id <<< "${targets[$project]}"
        echo ">>> 检测到 commit message or 构建脚本自带触发构建项目，包含 [$project]，准备触发项目 $project_id ($project)"
        echo ">>> 处理目标项目 $project_id 分支 '$CI_COMMIT_REF_NAME'"

        # 检查分支是否存在
        branch_url="https://git-internal.polyv.net/api/v4/projects/${project_id}/repository/branches/${CI_COMMIT_REF_NAME}"
        if curl --silent --fail -H "PRIVATE-TOKEN: ${GLOBAL_CI_TOKEN}" "$branch_url" > /dev/null; then
          echo "分支 '${CI_COMMIT_REF_NAME}' 在项目 ${project_id} 存在，准备提交 trigger"
        else
          echo "分支 '${CI_COMMIT_REF_NAME}' 在项目 ${project_id} 不存在，跳过该项目。"
          continue
        fi
        echo "触发$project webhook"
      
        project_url=$(curl --silent --header "PRIVATE-TOKEN: ${GLOBAL_CI_TOKEN}" "https://git-internal.polyv.net/api/v4/projects/${project_id}" \
          | grep -o '"http_url_to_repo":"[^"]*"' | sed -E 's/"http_url_to_repo":"([^"]*)"/\1/')
      
        payload='{
          "object_kind": "push",
          "event_name": "push",
          "ref": "refs/heads/'${CI_COMMIT_REF_NAME}'",
          "user_name": "[polyv-ci]",
          "project": {
            "name": "'${project}'",
            "git_http_url": "'${project_url}'"
          },
          "commits": [
            { "id": "[polyv-ci]", "message": "[polyv-ci]" }
          ]
        }'
      
        echo "req body: ${payload}"
        resp_code=$(curl -s -o /dev/null -w "%{http_code}" \
          -X POST "https://rider-api.igeeker.org/api/apps/cicd/webhooks" \
          -H "Content-Type: application/json" \
          -H "X-Gitlab-Event: Push Hook" \
          -d "$payload" || true)
      
        echo "rider-api 响应: HTTP $resp_code"
        if [[ ! "$resp_code" =~ ^2 ]]; then
          echo "警告：${project} 的 webhook 返回非 2xx（$resp_code），请检查接收端日志"
        fi
      done
  tags:
    - backend
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "trigger" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: on_success
